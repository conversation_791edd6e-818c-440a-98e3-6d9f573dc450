"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useAuth } from "@clerk/nextjs"
import { Check, Sparkles, Zap, Crown, ArrowRight, Bot } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

const plans = {
  monthly: [
    {
      name: "Starter",
      price: "Free",
      description: "Perfect for exploring AI-powered development",
      features: [
        "5 AI generations per month",
        "Basic code templates",
        "Community support",
        "Standard response time"
      ],
      limitations: [
        "Limited to 5 projects",
        "Basic AI models only"
      ],
      cta: "Get Started",
      popular: false,
      icon: Bot
    },
    {
      name: "Pro",
      price: "$29",
      description: "For developers building with AI at scale",
      features: [
        "Unlimited AI generations",
        "Advanced code templates",
        "Priority support",
        "Faster response times",
        "Advanced AI models",
        "Custom integrations",
        "Team collaboration",
        "Export capabilities"
      ],
      limitations: [],
      cta: "Upgrade to Pro",
      popular: true,
      icon: Zap
    },
    {
      name: "Enterprise",
      price: "Custom",
      description: "Tailored solutions for large teams",
      features: [
        "Everything in Pro",
        "Custom AI model training",
        "Dedicated support",
        "SLA guarantees",
        "On-premise deployment",
        "Advanced security",
        "Custom integrations",
        "Training & onboarding"
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false,
      icon: Crown
    }
  ],
  yearly: [
    {
      name: "Starter",
      price: "Free",
      description: "Perfect for exploring AI-powered development",
      features: [
        "5 AI generations per month",
        "Basic code templates",
        "Community support",
        "Standard response time"
      ],
      limitations: [
        "Limited to 5 projects",
        "Basic AI models only"
      ],
      cta: "Get Started",
      popular: false,
      icon: Bot
    },
    {
      name: "Pro",
      price: "$290",
      originalPrice: "$348",
      description: "For developers building with AI at scale",
      features: [
        "Unlimited AI generations",
        "Advanced code templates",
        "Priority support",
        "Faster response times",
        "Advanced AI models",
        "Custom integrations",
        "Team collaboration",
        "Export capabilities"
      ],
      limitations: [],
      cta: "Upgrade to Pro",
      popular: true,
      icon: Zap,
      savings: "Save $58"
    },
    {
      name: "Enterprise",
      price: "Custom",
      description: "Tailored solutions for large teams",
      features: [
        "Everything in Pro",
        "Custom AI model training",
        "Dedicated support",
        "SLA guarantees",
        "On-premise deployment",
        "Advanced security",
        "Custom integrations",
        "Training & onboarding"
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false,
      icon: Crown
    }
  ]
}

export default function Pricing() {
  const { has } = useAuth()
  const hasProAccess = has?.({ plan: "pro" })

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-16 max-w-7xl">
        {/* Header */}
        <div className="text-center space-y-6 mb-16">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/20 blur-xl rounded-full"></div>
              <Image
                src="/logo.svg"
                alt="Zyro"
                width={60}
                height={60}
                className="relative"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect plan for your AI-powered development journey.
              Scale as you grow.
            </p>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center">
            <Tabs defaultValue="monthly" className="w-fit">
              <TabsList className="grid w-full grid-cols-2 bg-muted/50 backdrop-blur-sm">
                <TabsTrigger value="monthly" className="data-[state=active]:bg-background">
                  Monthly
                </TabsTrigger>
                <TabsTrigger value="yearly" className="data-[state=active]:bg-background">
                  Yearly
                  <span className="ml-2 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                    Save 17%
                  </span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="monthly" className="mt-8">
                <PricingCards plans={plans.monthly} hasProAccess={hasProAccess} />
              </TabsContent>

              <TabsContent value="yearly" className="mt-8">
                <PricingCards plans={plans.yearly} hasProAccess={hasProAccess} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}

function PricingCards({ plans, hasProAccess }: { plans: any[], hasProAccess: boolean | undefined }) {
  return (
    <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      {plans.map((plan, index) => {
        const Icon = plan.icon
        const isCurrentPlan = hasProAccess && plan.name === "Pro"

        return (
          <Card
            key={plan.name}
            className={cn(
              "relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-105",
              plan.popular && "ring-2 ring-primary/50 shadow-xl scale-105",
              isCurrentPlan && "ring-2 ring-green-500/50"
            )}
          >
            {plan.popular && (
              <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground text-center py-2 text-sm font-medium">
                <Sparkles className="inline w-4 h-4 mr-1" />
                Most Popular
              </div>
            )}

            {plan.savings && (
              <div className="absolute top-4 right-4 bg-green-500/20 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                {plan.savings}
              </div>
            )}

            <CardHeader className={cn("text-center", plan.popular && "pt-12")}>
              <div className="flex justify-center mb-4">
                <div className={cn(
                  "p-3 rounded-xl",
                  plan.name === "Starter" && "bg-blue-500/20 text-blue-600",
                  plan.name === "Pro" && "bg-primary/20 text-primary",
                  plan.name === "Enterprise" && "bg-purple-500/20 text-purple-600"
                )}>
                  <Icon className="w-6 h-6" />
                </div>
              </div>

              <h3 className="text-2xl font-bold">{plan.name}</h3>
              <p className="text-muted-foreground text-sm">{plan.description}</p>

              <div className="flex items-baseline justify-center gap-2 mt-4">
                <span className="text-4xl font-bold">
                  {plan.price === "Free" ? "Free" : plan.price === "Custom" ? "Custom" : `$${plan.price}`}
                </span>
                {plan.originalPrice && (
                  <span className="text-lg text-muted-foreground line-through">
                    ${plan.originalPrice}
                  </span>
                )}
                {plan.price !== "Free" && plan.price !== "Custom" && (
                  <span className="text-muted-foreground">/year</span>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <Button
                className={cn(
                  "w-full",
                  plan.popular && "bg-primary hover:bg-primary/90",
                  isCurrentPlan && "bg-green-600 hover:bg-green-700"
                )}
                variant={plan.popular ? "default" : "outline"}
                asChild={!isCurrentPlan}
              >
                {isCurrentPlan ? (
                  <span>Current Plan</span>
                ) : (
                  <Link href={plan.name === "Enterprise" ? "/contact" : "/sign-up"}>
                    {plan.cta}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                )}
              </Button>

              <Separator />

              <div className="space-y-3">
                <h4 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">
                  What's included
                </h4>
                <ul className="space-y-3">
                  {plan.features.map((feature: string, featureIndex: number) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}