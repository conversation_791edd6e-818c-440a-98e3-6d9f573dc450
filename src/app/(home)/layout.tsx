import { Navbar } from "@/modules/home/<USER>/components/navbar";
import { ReactNode } from "react";

interface Props {
    children: ReactNode;
}

export default function HomeLayout({ children }: Props) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/10 relative">
            <Navbar />

            {/* Modern Background Pattern */}
            <div className="fixed inset-0 -z-10 w-screen h-screen">
                <div className="absolute inset-0 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]" />
                <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
                <div className="absolute bottom-0 left-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
            </div>

            <main className="relative z-10 pt-16">
                {children}
            </main>
        </div>
    );
}