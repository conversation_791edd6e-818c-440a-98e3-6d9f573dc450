"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap, Code, Cpu } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useAuth } from "@clerk/nextjs"

export function HeroSection() {
  const { isSignedIn } = useAuth()

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-primary/5" />
      
      {/* Animated Grid */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
      
      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 text-center space-y-8">
        {/* Badge */}
        <div className="flex justify-center">
          <Badge variant="secondary" className="px-4 py-2 text-sm bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors">
            <Sparkles className="w-4 h-4 mr-2" />
            AI-Powered Development Platform
          </Badge>
        </div>

        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="absolute inset-0 bg-primary/20 blur-2xl rounded-full animate-pulse" />
            <div className="relative bg-background/80 backdrop-blur-sm border border-primary/20 rounded-2xl p-6">
              <Image
                src="/logo.svg"
                alt="Zyro"
                width={80}
                height={80}
                className="relative"
              />
            </div>
          </div>
        </div>

        {/* Main Heading */}
        <div className="space-y-6">
          <h1 className="text-4xl md:text-7xl font-bold bg-gradient-to-r from-foreground via-foreground to-foreground/70 bg-clip-text text-transparent leading-tight">
            Build Anything with
            <br />
            <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              AI Assistance
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Transform your ideas into reality. Chat with AI to create apps, websites, and digital experiences in minutes, not hours.
          </p>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
          {isSignedIn ? (
            <Button size="lg" className="px-8 py-6 text-lg bg-primary hover:bg-primary/90 group">
              Start Building
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          ) : (
            <>
              <Button asChild size="lg" className="px-8 py-6 text-lg bg-primary hover:bg-primary/90 group">
                <Link href="/sign-up">
                  Get Started Free
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="px-8 py-6 text-lg border-primary/20 hover:bg-primary/5">
                <Link href="/pricing">
                  View Pricing
                </Link>
              </Button>
            </>
          )}
        </div>

        {/* Feature Pills */}
        <div className="flex flex-wrap justify-center gap-4 pt-12">
          <div className="flex items-center gap-2 px-4 py-2 bg-background/50 backdrop-blur-sm border border-border/50 rounded-full">
            <Code className="w-4 h-4 text-primary" />
            <span className="text-sm text-muted-foreground">Code Generation</span>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 bg-background/50 backdrop-blur-sm border border-border/50 rounded-full">
            <Zap className="w-4 h-4 text-primary" />
            <span className="text-sm text-muted-foreground">Instant Deployment</span>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 bg-background/50 backdrop-blur-sm border border-border/50 rounded-full">
            <Cpu className="w-4 h-4 text-primary" />
            <span className="text-sm text-muted-foreground">AI-Powered</span>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-muted-foreground/30 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>
    </section>
  )
}
