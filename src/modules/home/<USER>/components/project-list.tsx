"use client"

import Link from "next/link"
import Image from "next/image"
import { formatDistanceToNow } from "date-fns"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useTRPC } from "@/trpc/client"
import { useUser } from "@clerk/nextjs"
import { FolderOpen, Clock, ArrowRight, Plus } from "lucide-react"

export function ProjectList() {
    const { user } = useUser()
    const trpc = useTRPC()
    const { data: projects } = useQuery(trpc.projects.getMany.queryOptions())

    if (!user) return null;

    return (
        <section className="space-y-8">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="space-y-2">
                    <h2 className="text-2xl md:text-3xl font-bold">
                        Your Projects
                    </h2>
                    <p className="text-muted-foreground">
                        Continue working on your AI-generated applications
                    </p>
                </div>

                {projects && projects.length > 0 && (
                    <Badge variant="secondary" className="px-3 py-1">
                        {projects.length} project{projects.length !== 1 ? 's' : ''}
                    </Badge>
                )}
            </div>

            {/* Projects Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects?.length === 0 && (
                    <div className="col-span-full">
                        <Card className="border-dashed border-2 border-border/50 bg-background/50">
                            <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                                <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-4">
                                    <FolderOpen className="w-8 h-8 text-muted-foreground" />
                                </div>
                                <h3 className="text-lg font-semibold mb-2">No projects yet</h3>
                                <p className="text-muted-foreground mb-6 max-w-sm">
                                    Start building your first AI-powered application by describing your idea above.
                                </p>
                                <Button variant="outline" size="sm">
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Your First Project
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {projects?.map((project) => (
                    <Card
                        key={project.id}
                        className="group relative overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:scale-105"
                    >
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                                        <Image
                                            src="/logo.svg"
                                            alt="Zyro"
                                            width={20}
                                            height={20}
                                            className="object-contain"
                                        />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h3 className="font-semibold truncate group-hover:text-primary transition-colors">
                                            {project.name}
                                        </h3>
                                        <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                                            <Clock className="w-3 h-3" />
                                            {formatDistanceToNow(project.updatedAt, {
                                                addSuffix: true
                                            })}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardHeader>

                        <CardContent className="pt-0">
                            <Button
                                asChild
                                variant="ghost"
                                className="w-full justify-between p-0 h-auto hover:bg-transparent group-hover:text-primary"
                            >
                                <Link href={`/projects/${project.id}`}>
                                    <span className="text-sm">Open project</span>
                                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                                </Link>
                            </Button>
                        </CardContent>

                        {/* Hover Effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Card>
                ))}
            </div>
        </section>
    )
}