"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Users, Code, Zap } from "lucide-react"

const stats = [
  {
    icon: Users,
    value: "10K+",
    label: "Developers",
    description: "Building with AI",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    icon: Code,
    value: "50K+",
    label: "Projects",
    description: "Created & Deployed",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    icon: Zap,
    value: "99.9%",
    label: "Uptime",
    description: "Reliable Platform",
    gradient: "from-yellow-500 to-orange-500"
  },
  {
    icon: TrendingUp,
    value: "5x",
    label: "Faster",
    description: "Development Speed",
    gradient: "from-purple-500 to-pink-500"
  }
]

export function StatsSection() {
  return (
    <section className="py-16 relative">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5" />
      
      <div className="relative z-10 max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="px-4 py-2 bg-primary/10 text-primary border-primary/20 mb-6">
            Trusted by Developers
          </Badge>
          <h2 className="text-2xl md:text-4xl font-bold text-center mb-4">
            Join thousands of developers building the future
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Our platform empowers developers worldwide to create amazing applications faster than ever before.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card 
                key={stat.label}
                className="group relative overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                {/* Gradient Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                
                <CardContent className="relative p-6 text-center">
                  <div className="flex justify-center mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${stat.gradient} bg-opacity-10`}>
                      <Icon className={`w-6 h-6 bg-gradient-to-br ${stat.gradient} bg-clip-text text-transparent`} />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className={`text-3xl md:text-4xl font-bold bg-gradient-to-br ${stat.gradient} bg-clip-text text-transparent`}>
                      {stat.value}
                    </div>
                    <div className="font-semibold text-foreground">
                      {stat.label}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.description}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
