"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { UserControl } from "@/components/user-control";
import { useScroll } from "@/hooks/use-scroll";
import { cn } from "@/lib/utils";
import { SignedIn, SignedOut, SignInButton, SignUpButton } from "@clerk/nextjs";
import { <PERSON><PERSON>les, Menu, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export function Navbar() {
    const isScrolled = useScroll();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    return (
        <nav
            className={cn(
                "p-4 bg-transparent fixed top-0 left-0 right-0 z-50 transition-all duration-200 border-b border-transparent backdrop-blur-xl",
                isScrolled && "bg-background/80 border-border/40"
            )}
        >
            <div className="max-w-7xl mx-auto w-full flex justify-between items-center">
                {/* Logo */}
                <Link href="/" className="flex items-center gap-3 group">
                    <div className="relative">
                        <div className="absolute inset-0 bg-primary/20 blur-lg rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                        <div className="relative w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <Image
                                src="/logo.svg"
                                alt="Zyro"
                                width={24}
                                height={24}
                            />
                        </div>
                    </div>
                    <span className="font-bold text-xl bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                        Zyro
                    </span>
                    <Badge variant="secondary" className="hidden sm:inline-flex px-2 py-0.5 text-xs bg-primary/10 text-primary border-primary/20">
                        <Sparkles className="w-3 h-3 mr-1" />
                        AI
                    </Badge>
                </Link>

                {/* Desktop Navigation */}
                <div className="hidden md:flex items-center gap-6">
                    <div className="flex items-center gap-1">
                        <Button asChild variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                            <Link href="/pricing">
                                Pricing
                            </Link>
                        </Button>
                    </div>

                    <SignedOut>
                        <div className="flex items-center gap-3">
                            <SignInButton>
                                <Button variant="ghost" size="sm">
                                    Sign In
                                </Button>
                            </SignInButton>
                            <SignUpButton>
                                <Button size="sm" className="bg-primary hover:bg-primary/90">
                                    Get Started
                                </Button>
                            </SignUpButton>
                        </div>
                    </SignedOut>

                    <SignedIn>
                        <div className="flex items-center gap-3">
                            <Button asChild size="sm" variant="outline">
                                <Link href="/dashboard">
                                    Dashboard
                                </Link>
                            </Button>
                            <UserControl showName />
                        </div>
                    </SignedIn>
                </div>

                {/* Mobile Menu Button */}
                <div className="md:hidden">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    >
                        {isMobileMenuOpen ? (
                            <X className="w-5 h-5" />
                        ) : (
                            <Menu className="w-5 h-5" />
                        )}
                    </Button>
                </div>
            </div>

            {/* Mobile Menu */}
            {isMobileMenuOpen && (
                <div className="md:hidden border-t border-border/40 bg-background/95 backdrop-blur-xl">
                    <div className="px-4 py-6 space-y-4">
                        <div className="space-y-2">
                            <Button asChild variant="ghost" className="w-full justify-start" onClick={() => setIsMobileMenuOpen(false)}>
                                <Link href="/pricing">
                                    Pricing
                                </Link>
                            </Button>
                        </div>

                        <div className="pt-4 border-t border-border/40">
                            <SignedOut>
                                <div className="space-y-2">
                                    <SignInButton>
                                        <Button variant="ghost" className="w-full" onClick={() => setIsMobileMenuOpen(false)}>
                                            Sign In
                                        </Button>
                                    </SignInButton>
                                    <SignUpButton>
                                        <Button className="w-full bg-primary hover:bg-primary/90" onClick={() => setIsMobileMenuOpen(false)}>
                                            Get Started
                                        </Button>
                                    </SignUpButton>
                                </div>
                            </SignedOut>

                            <SignedIn>
                                <div className="space-y-2">
                                    <Button asChild variant="outline" className="w-full" onClick={() => setIsMobileMenuOpen(false)}>
                                        <Link href="/dashboard">
                                            Dashboard
                                        </Link>
                                    </Button>
                                    <div className="flex justify-center pt-2">
                                        <UserControl showName />
                                    </div>
                                </div>
                            </SignedIn>
                        </div>
                    </div>
                </div>
            )}
        </nav>
    )
}