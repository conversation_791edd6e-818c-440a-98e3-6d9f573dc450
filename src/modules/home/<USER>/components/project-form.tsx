"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormField } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useTRPC } from "@/trpc/client";
import { useClerk } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrowUpIcon, Loader2Icon, Sparkles, Wand2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import TextareaAutosize from "react-textarea-autosize";
import { toast } from "sonner";
import { z } from "zod";
import { PROJECT_TEMPLATES } from "../../constants";

const formSchema = z.object({
    value: z.string()
        .min(1, { message: "Message is required" })
        .max(10000, { message: "Message is too long" }),
})

export function ProjectForm() {
    const clerk = useClerk();
    const router = useRouter();
    const trpc = useTRPC();
    const queryClient = useQueryClient();
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            value: ""
        }
    })

    const createProject = useMutation(trpc.projects.create.mutationOptions({
        onSuccess: (data) => {
            queryClient.invalidateQueries(
                trpc.projects.getMany.queryOptions()
            )
            queryClient.invalidateQueries(
                trpc.usage.status.queryOptions()
            )
            router.push(`/projects/${data?.id}`)
        },
        onError: (error) => {
            toast.error(error.message);

            if (error.data?.code === "UNAUTHORIZED") {
                clerk.openSignIn();
            }

            if (error.data?.code === "TOO_MANY_REQUESTS") {
                router.push("/pricing")
            }
        }
    }))

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        await createProject.mutateAsync({
            value: values.value,
        })
    }

    const onSelect = (content: string) => {
        form.setValue("value", content, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true
        });
    }

    const [isFocused, setIsFocused] = useState(false);

    const isPending = createProject.isPending
    const isDisabled = isPending || !form.formState.isValid

    return (
        <section className="space-y-8">
            {/* Header */}
            <div className="text-center space-y-4">
                <Badge variant="secondary" className="px-4 py-2 bg-primary/10 text-primary border-primary/20">
                    <Wand2 className="w-4 h-4 mr-2" />
                    AI-Powered Development
                </Badge>
                <h2 className="text-2xl md:text-4xl font-bold">
                    What would you like to build today?
                </h2>
                <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Describe your idea and watch our AI transform it into a fully functional application.
                </p>
            </div>

            <Form {...form}>
                <div className="space-y-6">
                    {/* Main Input Form */}
                    <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className={cn(
                            "relative border-2 border-border/50 p-6 rounded-2xl bg-background/50 backdrop-blur-sm transition-all duration-300",
                            isFocused && "border-primary/50 shadow-xl shadow-primary/10 bg-background/80",
                        )}
                    >
                        <FormField
                            control={form.control}
                            name="value"
                            render={({ field }) => (
                                <TextareaAutosize
                                    {...field}
                                    disabled={isPending}
                                    onFocus={() => setIsFocused(true)}
                                    onBlur={() => setIsFocused(false)}
                                    minRows={3}
                                    maxRows={8}
                                    className="resize-none border-none w-full outline-none bg-transparent text-lg placeholder:text-muted-foreground/60"
                                    placeholder="Describe your app idea... (e.g., 'Build a task management app with drag-and-drop functionality')"
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
                                            e.preventDefault();
                                            form.handleSubmit(onSubmit)();
                                        }
                                    }}
                                />
                            )}
                        />

                        <div className="flex gap-x-3 items-center justify-between pt-4 border-t border-border/30">
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <kbd className="pointer-events-none inline-flex h-6 items-center gap-1 rounded border border-muted px-2 font-mono text-[10px] font-medium text-muted-foreground">
                                    <span>⌘</span> Enter
                                </kbd>
                                <span>to generate</span>
                            </div>

                            <Button
                                disabled={isDisabled}
                                size="lg"
                                className={cn(
                                    "px-6 py-3 rounded-xl bg-primary hover:bg-primary/90 group",
                                    isDisabled && "bg-muted-foreground/20 text-muted-foreground"
                                )}
                            >
                                {isPending ? (
                                    <>
                                        <Loader2Icon className="size-4 animate-spin mr-2" />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <Sparkles className="size-4 mr-2 group-hover:rotate-12 transition-transform" />
                                        Generate App
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>

                    {/* Template Suggestions */}
                    <div className="space-y-4">
                        <div className="text-center">
                            <p className="text-sm text-muted-foreground">
                                Or try one of these popular templates
                            </p>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-4xl mx-auto">
                            {PROJECT_TEMPLATES.map((template) => (
                                <Button
                                    key={template.title}
                                    variant="outline"
                                    size="sm"
                                    className="h-auto p-4 flex flex-col items-center gap-2 bg-background/50 backdrop-blur-sm border-border/50 hover:border-primary/50 hover:bg-primary/5 transition-all group"
                                    onClick={() => onSelect(template.prompt)}
                                >
                                    <span className="text-2xl group-hover:scale-110 transition-transform">
                                        {template.emoji}
                                    </span>
                                    <span className="text-xs font-medium text-center leading-tight">
                                        {template.title}
                                    </span>
                                </Button>
                            ))}
                        </div>
                    </div>
                </div>
            </Form>
        </section>
    )
}