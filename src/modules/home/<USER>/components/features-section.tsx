"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  <PERSON>t, 
  Code2, 
  <PERSON>ap, 
  Palette, 
  Globe, 
  Shield, 
  Sparkles,
  ArrowRight,
  Cpu,
  Layers,
  Rocket,
  Brain
} from "lucide-react"

const features = [
  {
    icon: Brain,
    title: "AI-Powered Code Generation",
    description: "Advanced AI understands your requirements and generates clean, production-ready code instantly.",
    badge: "Core Feature",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    icon: Zap,
    title: "Lightning Fast Development",
    description: "Build complete applications in minutes, not days. From concept to deployment in record time.",
    badge: "Speed",
    gradient: "from-yellow-500 to-orange-500"
  },
  {
    icon: Palette,
    title: "Beautiful UI Components",
    description: "Modern, responsive designs with customizable themes and components that look great everywhere.",
    badge: "Design",
    gradient: "from-pink-500 to-purple-500"
  },
  {
    icon: Globe,
    title: "Instant Deployment",
    description: "Deploy your applications instantly with our integrated hosting platform. No configuration needed.",
    badge: "Deployment",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Built-in security best practices, authentication, and data protection for peace of mind.",
    badge: "Security",
    gradient: "from-red-500 to-pink-500"
  },
  {
    icon: Layers,
    title: "Full-Stack Solutions",
    description: "Complete applications with frontend, backend, database, and API integrations out of the box.",
    badge: "Complete",
    gradient: "from-indigo-500 to-blue-500"
  }
]

export function FeaturesSection() {
  return (
    <section className="py-24 relative">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center space-y-6 mb-16">
          <Badge variant="secondary" className="px-4 py-2 bg-primary/10 text-primary border-primary/20">
            <Sparkles className="w-4 h-4 mr-2" />
            Powerful Features
          </Badge>
          
          <h2 className="text-3xl md:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Everything you need to build
            <br />
            <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              amazing applications
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our AI-powered platform provides all the tools and features you need to create, deploy, and scale your applications.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <Card 
                key={feature.title}
                className="group relative overflow-hidden border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:scale-105"
              >
                {/* Gradient Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                
                <CardHeader className="relative">
                  <div className="flex items-start justify-between">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${feature.gradient} bg-opacity-10`}>
                      <Icon className={`w-6 h-6 bg-gradient-to-br ${feature.gradient} bg-clip-text text-transparent`} />
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  
                  <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
                    {feature.title}
                  </h3>
                </CardHeader>
                
                <CardContent className="relative">
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                  
                  <div className="flex items-center mt-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="text-sm font-medium">Learn more</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-primary/10 border border-primary/20 rounded-full text-primary">
            <Rocket className="w-5 h-5" />
            <span className="font-medium">Ready to start building?</span>
          </div>
        </div>
      </div>
    </section>
  )
}
