# This is a config for E2B sandbox template.
# You can use template ID (j85jfg77r9ov4u9jaemc) or template name (zyro-nextjs-alpine-1752167437) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("zyro-nextjs-alpine-1752167437") # Sync sandbox
# sandbox = await AsyncSandbox.create("zyro-nextjs-alpine-1752167437") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('zyro-nextjs-alpine-1752167437')

team_id = "44da2f19-6eda-4c99-8284-d361e170f873"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "zyro-nextjs-alpine-1752167437"
template_id = "j85jfg77r9ov4u9jaemc"
