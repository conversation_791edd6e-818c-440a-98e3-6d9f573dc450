# This is a config for E2B sandbox template.
# You can use template ID (x6ragr470kqd5bvm0cwx) or template name (zyro-nextjs-riaz302001) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("zyro-nextjs-riaz302001") # Sync sandbox
# sandbox = await AsyncSandbox.create("zyro-nextjs-riaz302001") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('zyro-nextjs-riaz302001')

team_id = "44da2f19-6eda-4c99-8284-d361e170f873"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "zyro-nextjs-riaz302001"
template_id = "x6ragr470kqd5bvm0cwx"
